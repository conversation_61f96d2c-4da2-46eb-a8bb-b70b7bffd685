import React, { useEffect, useContext } from "react";
import { View, ScrollView, Platform, NativeModules, Image, Text, TouchableOpacity, BackHandler } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { StackScreenProps } from "@react-navigation/stack";
import { RootStackParamList, RootNavigationProps } from "router/type";
import { usePageReport } from "hooks/usePageReport";
import { getStyles } from "./style";
import Header from "components/DrinkWater/Header";
import EightGlasses from "components/DrinkWater/EightGlasses";
import WaterTip from "components/DrinkWater/WaterTip";
import WaterWeekCard from "components/DrinkWater/WaterWeek";
import WaterFullWeekCard from "components/DrinkWater/WaterFullWeek";
import { PageEventEmitter } from "defs";
import GlobalEventEmitter from "utilsV2/globalEventEmitter";
import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { waterInfoAtom, writeWaterInfo<PERSON>tom } from "./store";
import { Page } from "@xmly/rn-sdk";
import getUrlToOpen from "utilsV2/getUrlToOpen";
import xmlog from '../../utilsV2/xmlog'
import getXMRequestId from 'utilsV2/getXMRequestId';
import { NativeInfoContext } from "contextV2/nativeInfoContext";
import navigationService from "../../services/navigationService";

const headBg = 'https://imagev2.xmcdn.com/storages/7aba-audiofreehighqps/93/48/GKwRIJIMOXqeAAJdqwPWsMgy.png'

export default function DrinkWater(props: StackScreenProps<RootStackParamList>) {
  const styles = getStyles();
  const navigation = useNavigation<RootNavigationProps>();
  const nativeInfo = useContext(NativeInfoContext);

  const waterInfo = useAtomValue(waterInfoAtom);
  const [xmRequestId, setXmRequestId] = React.useState('');

  usePageReport({
    pageViewCode: 69117, 
    pageExitCode: 69118,
    currPage: 'drinkWaterActivityPage',
    otherProps: props
  });

  useEffect(() => {
    // 去掉骨架屏
    GlobalEventEmitter.emit('appContentReady');

    // 自定义返回处理函数
    const handleCustomBack = () => {
      console.log('DrinkWater: handleCustomBack called natigation.canGoBack', navigation.canGoBack());
      try {
        if (navigation.canGoBack()) {
          console.log('DrinkWater: Using default back logic');
          navigation.goBack();
        } else {
          navigationService.goBackFromDrinkWater();
        }
      } catch (error) {
        console.error('DrinkWater: Error in handleCustomBack:', error);
      }
    };

    // Android硬件返回键处理
    const handleAndroidBackPress = () => {
      console.log('DrinkWater: Android back press, route:', nativeInfo?.route);
      // 如果是通过 route=drinkWater 进入的，使用自定义返回逻辑
      if (nativeInfo?.route === 'drinkWater') {
        console.log('DrinkWater: Handling custom back for drinkWater route');
        handleCustomBack();
        return true; // 阻止默认返回行为
      }
      console.log('DrinkWater: Using default back behavior');
      return false; // 允许默认返回行为
    };

    // iOS手势返回处理 - 使用beforeRemove事件
    const handleIosBeforeRemove = (e: any) => {
      // 只在iOS上处理beforeRemove事件，避免与Android的BackHandler冲突
      if (Platform.OS === 'ios' && nativeInfo?.route === 'drinkWater') {
        // 阻止默认行为
        e.preventDefault();
        // 执行自定义返回逻辑
        handleCustomBack();
      }
      // 如果不是iOS或不是通过 route=drinkWater 进入的，允许默认返回行为
    };

    // 根据平台注册不同的事件监听器
    const backHandler = Platform.OS === 'android'
      ? BackHandler.addEventListener('hardwareBackPress', handleAndroidBackPress)
      : null;

    const beforeRemoveListener = Platform.OS === 'ios'
      ? navigation.addListener('beforeRemove', handleIosBeforeRemove)
      : null;

    const blurListener = navigation.addListener('blur', () => {
      if (Platform.OS == 'ios') {
        NativeModules.CompatibleIOS.setSwipBackGestureEnable(false)
      }
    });

    const focusListener = navigation.addListener('focus', () => {
      if (Platform.OS == 'ios') {
        NativeModules.CompatibleIOS.setSwipBackGestureEnable(true)
      }
    });

    const resumeListener = PageEventEmitter.addListener('onResume', () => {
      console.log("DrinkWater onResuming....................")
      // 可以在这里刷新数据
    });

    return () => {
      backHandler?.remove();
      beforeRemoveListener?.();
      blurListener?.();
      focusListener?.();
      resumeListener?.remove();
    }
  }, [navigation, nativeInfo?.route]);

  useEffect(() => {
    (async () => {
      const reqId = await getXMRequestId();
      setXmRequestId(reqId);
    })();
  }, []);

  function handleRulePress() {
    if (waterInfo?.rulePage) {
      xmlog.click(69119, undefined, {
        currPage: 'drinkWaterActivityPage',
        Item: '规则',
        xmRequestId,
      })
      Page.start(getUrlToOpen(waterInfo.rulePage));
    }
  }

  return (
    <View style={styles.container}>
      <Header />
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        bounces={false}
      >
        <Image
          source={{ uri: headBg }}
          style={styles.backgroundImage}
        />
        {waterInfo?.rulePage? (
          <TouchableOpacity
            style={styles.ruleButton}
            onPress={handleRulePress}
            activeOpacity={0.8}
          >
            <Text style={styles.ruleText}>规则</Text>
          </TouchableOpacity>
        ) : null}
        <View style={styles.contentWrapper}>
          <EightGlasses xmRequestId={xmRequestId} />
          <WaterTip />
          {waterInfo?.waterWeek && (
            <WaterWeekCard
              waterWeek={waterInfo.waterWeek}
              xmRequestId={xmRequestId}
            />
          )}
          {waterInfo?.waterFullWeek && (
            <WaterFullWeekCard
              waterFullWeek={waterInfo.waterFullWeek}
              xmRequestId={xmRequestId}
            />
          )}
        </View>
      </ScrollView>
    </View>
  );
}
